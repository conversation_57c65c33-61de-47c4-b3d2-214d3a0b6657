# Git Workflow Guide

This document outlines the modern Git workflow practices implemented in this project, replacing the deprecated <PERSON>sky setup.

## Overview

We've migrated from Husky to a more modern, efficient Git workflow using:

- **simple-git-hooks**: Lightweight Git hooks management
- **commitlint**: Conventional commit message enforcement
- **semantic-release**: Automated versioning and releases
- **GitHub Actions**: CI/CD automation
- **lint-staged**: Code quality checks on staged files

## Commit Message Convention

We follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
<type>(<scope>): <subject>

<body>

<footer>
```

### Types

- `feat`: A new feature
- `fix`: A bug fix
- `docs`: Documentation only changes
- `style`: Changes that do not affect the meaning of the code
- `refactor`: A code change that neither fixes a bug nor adds a feature
- `perf`: A code change that improves performance
- `test`: Adding missing tests or correcting existing tests
- `build`: Changes that affect the build system or external dependencies
- `ci`: Changes to our CI configuration files and scripts
- `chore`: Other changes that don't modify src or test files
- `revert`: Reverts a previous commit

### Examples

```bash
feat(auth): add OAuth2 integration
fix(ui): resolve button alignment issue
docs(readme): update installation instructions
refactor(api): simplify user service logic
```

## Development Workflow

### 1. Making Commits

#### Option A: Use Commitizen (Recommended)

```bash
npm run commit
```

This will guide you through creating a properly formatted commit message.

#### Option B: Manual Commits

```bash
git add .
git commit -m "feat(auth): add user profile management"
```

### 2. Pre-commit Hooks

The following checks run automatically before each commit:

- **Prettier**: Code formatting
- **ESLint**: Code linting and fixes
- **TypeScript**: Type checking

### 3. Commit Message Validation

Commit messages are automatically validated against our conventional commit rules.

## Branch Strategy

### Main Branches

- `main`/`master`: Production-ready code
- `develop`: Integration branch for features

### Feature Branches

- `feature/feature-name`: New features
- `fix/bug-description`: Bug fixes
- `docs/documentation-update`: Documentation changes

### Release Branches

- `release/v1.0.0`: Release preparation
- `hotfix/critical-fix`: Critical production fixes

## Automated Releases

Releases are automatically generated based on commit messages:

- `feat`: Minor version bump (1.0.0 → 1.1.0)
- `fix`: Patch version bump (1.0.0 → 1.0.1)
- `BREAKING CHANGE`: Major version bump (1.0.0 → 2.0.0)

### Release Process

1. Commits are analyzed for semantic meaning
2. Version number is determined automatically
3. Changelog is generated
4. Git tag is created
5. GitHub release is published

## GitHub Actions Workflows

### CI Workflow (`.github/workflows/ci.yml`)

Runs on every push and pull request:

- Code linting (ESLint)
- Type checking (TypeScript)
- Code formatting check (Prettier)
- Build verification
- Security audit

### Release Workflow (`.github/workflows/release.yml`)

Runs on pushes to main branch:

- Full test suite
- Automated semantic release
- Changelog generation
- Version tagging

### Dependency Update (`.github/workflows/dependency-update.yml`)

Runs weekly:

- Updates dependencies
- Creates pull request if changes detected
- Runs full test suite

## Setup Instructions

### 1. Install Dependencies

```bash
npm install
```

### 2. Initialize Git Hooks

```bash
npm run prepare
```

### 3. Configure Git Template (Optional)

```bash
git config commit.template .gitmessage
```

## Available Scripts

- `npm run commit`: Interactive commit with Commitizen
- `npm run validate`: Run all quality checks
- `npm run format`: Format code with Prettier
- `npm run lint`: Run ESLint
- `npm run lint:fix`: Fix ESLint issues
- `npm run check-types`: TypeScript type checking
- `npm run release`: Manual semantic release (CI only)

## Migration from Husky

The old Husky configuration has been replaced with:

1. **simple-git-hooks**: More performant and simpler
2. **Conventional commits**: Better commit message standards
3. **Automated releases**: Semantic versioning and changelog generation
4. **Enhanced CI/CD**: Comprehensive GitHub Actions workflows

### What Changed

- Removed `husky` dependency
- Added `simple-git-hooks`, `commitlint`, and `semantic-release`
- Enhanced package.json scripts
- Added GitHub Actions workflows
- Implemented conventional commit standards

## Troubleshooting

### Git Hooks Not Working

```bash
npm run prepare
```

### Commit Message Rejected

Ensure your commit message follows the conventional format:

```bash
git commit -m "type(scope): description"
```

### CI Failures

Check the GitHub Actions tab for detailed error logs and fix issues before merging.

## Best Practices

1. **Write meaningful commit messages**: Follow conventional commit format
2. **Keep commits atomic**: One logical change per commit
3. **Use feature branches**: Don't commit directly to main
4. **Review before merging**: Use pull requests for code review
5. **Keep dependencies updated**: Monitor automated dependency PRs
6. **Test before committing**: Pre-commit hooks will catch issues early

## Resources

- [Conventional Commits](https://www.conventionalcommits.org/)
- [Semantic Release](https://semantic-release.gitbook.io/)
- [GitHub Actions](https://docs.github.com/en/actions)
- [ESLint](https://eslint.org/)
- [Prettier](https://prettier.io/)
