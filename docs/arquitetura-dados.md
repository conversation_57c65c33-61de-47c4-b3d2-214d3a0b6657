# Arquitetura de Dados - Gemnial Platform

## Modelo de Dados Principal

### Entidades Fundamentais

- User (id, email, role, profile_data)
- Course (id, title, description, syllabus_structure)
- Module (id, course_id, title, order, prerequisites)
- Lesson (id, module_id, title, content, interactive_elements)
- Question (id, topic_ids, difficulty, content, answers, explanation)
- UserProgress (user_id, lesson_id, completed, score, time_spent)
- UserInteraction (id, user_id, interaction_type, context, timestamp)

### Modelos para IA Adaptativa

- UserSkillProfile (user_id, skill_id, proficiency_score, confidence)
- ContentRecommendation (user_id, content_id, relevance_score, reason)
- LearningPathNode (id, prerequisite_nodes, content_id, difficulty)
