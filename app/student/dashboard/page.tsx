import Link from 'next/link';

export default function StudentDashboard() {
  return (
    <div>
      <div className="pb-5 border-b border-gray-200">
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="mt-2 text-sm text-gray-600">
          Welcome back! Here&apos;s what&apos;s happening with your courses.
        </p>
      </div>

      <div className="mt-8">
        <h2 className="text-lg font-medium text-gray-900">Your Courses</h2>
        <div className="mt-4 grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
          {/* Course Card 1 */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-blue-500 rounded-md p-3">
                  <svg
                    className="h-6 w-6 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      Introduction to Programming
                    </dt>
                    <dd>
                      <div className="text-lg font-medium text-gray-900">Week 3: Functions</div>
                    </dd>
                  </dl>
                </div>
              </div>
              <div className="mt-4">
                <div className="bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-500 rounded-full h-2" style={{ width: '65%' }} />
                </div>
                <div className="mt-2 text-sm text-gray-500">65% complete</div>
              </div>
              <div className="mt-4">
                <Link
                  href="/student/courses/intro-to-programming"
                  className="text-sm font-medium text-blue-600 hover:text-blue-500"
                >
                  Continue Learning<span> &rarr;</span>
                </Link>
              </div>
            </div>
          </div>

          {/* Course Card 2 */}
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0 bg-green-500 rounded-md p-3">
                  <svg
                    className="h-6 w-6 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                    />
                  </svg>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Web Development</dt>
                    <dd>
                      <div className="text-lg font-medium text-gray-900">Week 1: HTML & CSS</div>
                    </dd>
                  </dl>
                </div>
              </div>
              <div className="mt-4">
                <div className="bg-gray-200 rounded-full h-2">
                  <div className="bg-green-500 rounded-full h-2" style={{ width: '20%' }} />
                </div>
                <div className="mt-2 text-sm text-gray-500">20% complete</div>
              </div>
              <div className="mt-4">
                <Link
                  href="/student/courses/web-dev"
                  className="text-sm font-medium text-blue-600 hover:text-blue-500"
                >
                  Continue Learning<span> &rarr;</span>
                </Link>
              </div>
            </div>
          </div>

          {/* Add Course Card */}
          <div className="bg-white overflow-hidden shadow rounded-lg border-2 border-dashed border-gray-300 hover:border-blue-400">
            <div className="p-5 h-full flex items-center justify-center">
              <div className="text-center">
                <svg
                  className="mx-auto h-12 w-12 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
                <h3 className="mt-2 text-sm font-medium text-gray-900">Browse courses</h3>
                <p className="mt-1 text-sm text-gray-500">Discover new learning opportunities</p>
                <div className="mt-4">
                  <Link
                    href="/student/courses"
                    className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Browse All Courses
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-8">
        <h2 className="text-lg font-medium text-gray-900">Upcoming Sessions</h2>
        <div className="mt-4 overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg">
          <table className="min-w-full divide-y divide-gray-300">
            <thead className="bg-gray-50">
              <tr>
                <th
                  scope="col"
                  className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"
                >
                  Course
                </th>
                <th
                  scope="col"
                  className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                >
                  Date & Time
                </th>
                <th
                  scope="col"
                  className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                >
                  Tutor
                </th>
                <th
                  scope="col"
                  className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                >
                  Join
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white">
              <tr>
                <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                  <div className="flex items-center">
                    <div>
                      <div className="font-medium text-gray-900">Introduction to Programming</div>
                      <div className="text-gray-500">Week 3: Functions</div>
                    </div>
                  </div>
                </td>
                <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                  <div>May 30, 2023</div>
                  <div className="text-gray-500">2:00 PM - 3:30 PM</div>
                </td>
                <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                  <div className="flex items-center">
                    <div className="h-10 w-10 flex-shrink-0">
                      <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 font-medium">
                        T
                      </div>
                    </div>
                    <div className="ml-4">
                      <div className="font-medium text-gray-900">John Smith</div>
                      <div className="text-gray-500">Senior Instructor</div>
                    </div>
                  </div>
                </td>
                <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                  <Link href="#" className="text-blue-600 hover:text-blue-900">
                    Join Now<span className="sr-only">, Introduction to Programming</span>
                  </Link>
                </td>
              </tr>
              <tr>
                <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm sm:pl-6">
                  <div className="flex items-center">
                    <div>
                      <div className="font-medium text-gray-900">Web Development</div>
                      <div className="text-gray-500">Week 2: CSS Fundamentals</div>
                    </div>
                  </div>
                </td>
                <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                  <div>June 2, 2023</div>
                  <div className="text-gray-500">10:00 AM - 11:30 AM</div>
                </td>
                <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                  <div className="flex items-center">
                    <div className="h-10 w-10 flex-shrink-0">
                      <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center text-gray-600 font-medium">
                        S
                      </div>
                    </div>
                    <div className="ml-4">
                      <div className="font-medium text-gray-900">Sarah Johnson</div>
                      <div className="text-gray-500">Web Developer</div>
                    </div>
                  </div>
                </td>
                <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                  <span className="text-gray-400">Join link available soon</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
