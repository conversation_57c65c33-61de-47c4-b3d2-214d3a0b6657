'use client';

import { useState } from 'react';
import { createBrowserSupabaseClient } from '@/lib/supabaseClient';
import AuthForm from '@/components/auth/AuthForm';

export default function LoginPage() {
  const [supabase] = useState(() => createBrowserSupabaseClient());

  const handleLogin = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      throw new Error(error.message);
    }
  };

  return <AuthForm type="login" onSubmit={handleLogin} />;
}
