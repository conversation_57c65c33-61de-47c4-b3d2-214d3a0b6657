'use client';

import { useState } from 'react';
import { createBrowserSupabaseClient } from '@/lib/supabaseClient';
import AuthForm from '@/components/auth/AuthForm';

export default function SignUpPage() {
  const [supabase] = useState(() => createBrowserSupabaseClient());

  const handleSignUp = async (email: string, password: string, name?: string, role?: string) => {
    if (!name || !role) {
      throw new Error('Nome e tipo de conta são obrigatórios');
    }

    // Register user in Supabase Auth with autoconfirm
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: name,
          role: role,
        },
        emailRedirectTo: `${window.location.origin}/auth/callback`,
      },
    });

    if (authError) {
      throw new Error(authError.message);
    }

    if (!authData.user) {
      throw new Error('Erro ao criar usuário');
    }

    // Use a sessão já criada pelo signUp (se disponível)
    if (authData.session) {
      // Create profile using the session from signup
      const { error: profileError } = await supabase.from('profiles').insert({
        id: authData.user.id,
        email: email,
        first_name: name.split(' ')[0],
        last_name: name.split(' ').slice(1).join(' '),
        role: role as 'student' | 'tutor',
      });

      if (profileError) {
        throw new Error(`Erro ao criar perfil: ${profileError.message}`);
      }

      return { success: true, session: authData.session };
    } else {
      // Se não há sessão, significa que o email precisa ser confirmado
      return { success: true, emailConfirmationRequired: true };
    }
  };

  return <AuthForm type="signup" onSubmit={handleSignUp} />;
}
