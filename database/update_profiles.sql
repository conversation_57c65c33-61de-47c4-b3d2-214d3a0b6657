-- Alt<PERSON>r a tabela profiles para adicionar a coluna email e substituir name por first_name e last_name
ALTER TABLE profiles 
  ADD COLUMN IF NOT EXISTS email TEXT,
  ADD COLUMN IF NOT EXISTS first_name TEXT,
  ADD COLUMN IF NOT EXISTS last_name TEXT;

-- <PERSON><PERSON> a coluna email obrigatória após adicionar a coluna (com valor padrão temporário)
UPDATE profiles SET email = '<EMAIL>' WHERE email IS NULL;
ALTER TABLE profiles 
  ALTER COLUMN email SET NOT NULL;

-- Remover a coluna name se ela existir (opcional, pode manter se preferir)
-- ALTER TABLE profiles DROP COLUMN IF EXISTS name;

-- Limpar o cache do esquema
SELECT pg_catalog.pg_reload_conf();
