-- <PERSON><PERSON><PERSON> da tabela de perfis (profiles)
CREATE TABLE IF NOT EXISTS profiles (
  id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
  role TEXT NOT NULL CHECK (role IN ('student', 'tutor')),
  email TEXT NOT NULL,
  first_name TEXT,
  last_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Índices para a tabela profiles
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role);

-- <PERSON><PERSON><PERSON> da tabela de disciplinas (subjects)
CREATE TABLE IF NOT EXISTS subjects (
  id SERIAL PRIMARY KEY,
  name TEXT UNIQUE NOT NULL
);

-- <PERSON><PERSON><PERSON> da tabela de tópicos (topics)
CREATE TABLE IF NOT EXISTS topics (
  id SERIAL PRIMARY KEY,
  subject_id INTEGER REFERENCES subjects(id) ON DELETE CASCADE,
  name TEXT NOT NULL
);

-- Índices para a tabela topics
CREATE INDEX IF NOT EXISTS idx_topics_subject_id ON topics(subject_id);

-- <PERSON><PERSON>ção da tabela de habilidades do ENEM (enem_skills)
CREATE TABLE IF NOT EXISTS enem_skills (
  id SERIAL PRIMARY KEY,
  code TEXT UNIQUE NOT NULL,
  description TEXT
);

-- Índices para a tabela enem_skills
CREATE INDEX IF NOT EXISTS idx_enem_skills_code ON enem_skills(code);

-- Criação da tabela de questões (questions)
CREATE TABLE IF NOT EXISTS questions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  topic_id INTEGER REFERENCES topics(id),
  enem_skill_id INTEGER REFERENCES enem_skills(id),
  content JSONB NOT NULL CHECK (jsonb_typeof(content) = 'object'),
  correct_option TEXT NOT NULL,
  created_by_tutor_id UUID REFERENCES profiles(id) ON DELETE SET NULL,
  difficulty_level_by_tutor INTEGER CHECK (difficulty_level_by_tutor BETWEEN 1 AND 5),
  ai_generated_explanation TEXT,
  tutor_validated_explanation TEXT,
  status TEXT NOT NULL DEFAULT 'pending_validation' CHECK (status IN ('pending_validation', 'validated', 'rejected', 'ai_generated')),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Índices para a tabela questions
CREATE INDEX IF NOT EXISTS idx_questions_topic_id ON questions(topic_id);
CREATE INDEX IF NOT EXISTS idx_questions_enem_skill_id ON questions(enem_skill_id);
CREATE INDEX IF NOT EXISTS idx_questions_created_by_tutor_id ON questions(created_by_tutor_id);
CREATE INDEX IF NOT EXISTS idx_questions_status ON questions(status);

-- Criação da tabela de respostas dos estudantes (student_answers)
CREATE TABLE IF NOT EXISTS student_answers (
  id SERIAL PRIMARY KEY,
  student_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  question_id UUID REFERENCES questions(id) ON DELETE CASCADE,
  selected_option TEXT,
  is_correct BOOLEAN,
  response_time_ms INTEGER,
  submitted_at TIMESTAMPTZ DEFAULT NOW()
);

-- Índices para a tabela student_answers
CREATE INDEX IF NOT EXISTS idx_student_answers_student_id ON student_answers(student_id);
CREATE INDEX IF NOT EXISTS idx_student_answers_question_id ON student_answers(question_id);
CREATE INDEX IF NOT EXISTS idx_student_answers_is_correct ON student_answers(is_correct);

-- Criação da tabela de interações dos estudantes (student_interactions)
CREATE TABLE IF NOT EXISTS student_interactions (
  id SERIAL PRIMARY KEY,
  student_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  question_id UUID REFERENCES questions(id) ON DELETE SET NULL,
  event_type TEXT NOT NULL,
  event_data JSONB,
  occurred_at TIMESTAMPTZ DEFAULT NOW()
);

-- Índices para a tabela student_interactions
CREATE INDEX IF NOT EXISTS idx_student_interactions_student_id ON student_interactions(student_id);
CREATE INDEX IF NOT EXISTS idx_student_interactions_question_id ON student_interactions(question_id);
CREATE INDEX IF NOT EXISTS idx_student_interactions_event_type ON student_interactions(event_type);
CREATE INDEX IF NOT EXISTS idx_student_interactions_occurred_at ON student_interactions(occurred_at);

-- Configuração de RLS (Row Level Security)
-- Habilitar RLS para todas as tabelas
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE subjects ENABLE ROW LEVEL SECURITY;
ALTER TABLE topics ENABLE ROW LEVEL SECURITY;
ALTER TABLE enem_skills ENABLE ROW LEVEL SECURITY;
ALTER TABLE questions ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_answers ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_interactions ENABLE ROW LEVEL SECURITY;

-- Políticas de RLS para a tabela profiles

-- Política para permitir que usuários leiam apenas seus próprios perfis
CREATE POLICY "Usuários podem ver seus próprios perfis"
  ON profiles FOR SELECT
  USING (auth.uid() = id);

-- Política para permitir que tutores vejam perfis de alunos
CREATE POLICY "Tutores podem ver perfis de alunos"
  ON profiles FOR SELECT
  USING (
    (SELECT role FROM profiles WHERE id = auth.uid()) = 'tutor' 
    AND role = 'student'
  );

-- Política para permitir que usuários atualizem apenas seus próprios perfis
CREATE POLICY "Usuários podem atualizar seus próprios perfis"
  ON profiles FOR UPDATE
  USING (auth.uid() = id);

-- Função para atualizar o campo updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers para atualizar o campo updated_at
CREATE TRIGGER update_profiles_updated_at
  BEFORE UPDATE ON profiles
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_questions_updated_at
  BEFORE UPDATE ON questions
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Adicionando políticas básicas para as outras tabelas

-- Subjects (leitura pública, escrita apenas por tutores)
CREATE POLICY "Leitura pública de disciplinas"
  ON subjects FOR SELECT
  USING (true);

CREATE POLICY "Apenas tutores podem modificar disciplinas"
  ON subjects FOR ALL
  USING ((SELECT role FROM profiles WHERE id = auth.uid()) = 'tutor');

-- Topics (leitura pública, escrita apenas por tutores)
CREATE POLICY "Leitura pública de tópicos"
  ON topics FOR SELECT
  USING (true);

CREATE POLICY "Apenas tutores podem modificar tópicos"
  ON topics FOR ALL
  USING ((SELECT role FROM profiles WHERE id = auth.uid()) = 'tutor');

-- ENEM Skills (leitura pública, escrita apenas por tutores)
CREATE POLICY "Leitura pública de habilidades do ENEM"
  ON enem_skills FOR SELECT
  USING (true);

CREATE POLICY "Apenas tutores podem modificar habilidades do ENEM"
  ON enem_skills FOR ALL
  USING ((SELECT role FROM profiles WHERE id = auth.uid()) = 'tutor');

-- Questions (leitura pública, escrita apenas por tutores)
CREATE POLICY "Leitura pública de questões"
  ON questions FOR SELECT
  USING (true);

CREATE POLICY "Apenas tutores podem criar questões"
  ON questions FOR INSERT
  WITH CHECK ((SELECT role FROM profiles WHERE id = auth.uid()) = 'tutor');

CREATE POLICY "Tutores podem atualizar questões que criaram"
  ON questions FOR UPDATE
  USING (
    (SELECT role FROM profiles WHERE id = auth.uid()) = 'tutor' 
    AND (created_by_tutor_id = auth.uid() OR created_by_tutor_id IS NULL)
  );

-- Student Answers (estudantes veem apenas suas próprias respostas, tutores veem todas)
CREATE POLICY "Estudantes veem apenas suas próprias respostas"
  ON student_answers FOR SELECT
  USING (
    student_id = auth.uid() 
    OR (SELECT role FROM profiles WHERE id = auth.uid()) = 'tutor'
  );

CREATE POLICY "Estudantes podem inserir apenas suas próprias respostas"
  ON student_answers FOR INSERT
  WITH CHECK (
    student_id = auth.uid() 
    AND (SELECT role FROM profiles WHERE id = auth.uid()) = 'student'
  );

-- Student Interactions (estudantes veem apenas suas próprias interações, tutores veem todas)
CREATE POLICY "Estudantes veem apenas suas próprias interações"
  ON student_interactions FOR SELECT
  USING (
    student_id = auth.uid() 
    OR (SELECT role FROM profiles WHERE id = auth.uid()) = 'tutor'
  );

CREATE POLICY "Estudantes podem inserir apenas suas próprias interações"
  ON student_interactions FOR INSERT
  WITH CHECK (
    student_id = auth.uid() 
    AND (SELECT role FROM profiles WHERE id = auth.uid()) = 'student'
  );
