-- Safe fix for missing profiles - works with any table structure
-- This script adapts to the current table structure

-- Step 1: Check what columns exist in profiles table
DO $$
DECLARE
    has_email BOOLEAN := FALSE;
    has_first_name BOOLEAN := FALSE;
    has_last_name BOOLEAN := FALSE;
    has_name BOOLEAN := FALSE;
BEGIN
    -- Check if email column exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' 
        AND column_name = 'email'
        AND table_schema = 'public'
    ) INTO has_email;
    
    -- Check if first_name column exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' 
        AND column_name = 'first_name'
        AND table_schema = 'public'
    ) INTO has_first_name;
    
    -- Check if last_name column exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' 
        AND column_name = 'last_name'
        AND table_schema = 'public'
    ) INTO has_last_name;
    
    -- Check if name column exists (old structure)
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' 
        AND column_name = 'name'
        AND table_schema = 'public'
    ) INTO has_name;
    
    RAISE NOTICE 'Table structure: email=%, first_name=%, last_name=%, name=%', 
                 has_email, has_first_name, has_last_name, has_name;
END $$;

-- Step 2: Add missing columns if needed
ALTER TABLE profiles 
  ADD COLUMN IF NOT EXISTS email TEXT,
  ADD COLUMN IF NOT EXISTS first_name TEXT,
  ADD COLUMN IF NOT EXISTS last_name TEXT;

-- Step 3: Fix RLS policies to prevent infinite recursion
DROP POLICY IF EXISTS "Tutores podem ver perfis de alunos" ON profiles;

-- Ensure INSERT policy exists
DROP POLICY IF EXISTS "Usuários podem inserir seus próprios perfis" ON profiles;
CREATE POLICY "Usuários podem inserir seus próprios perfis"
  ON profiles FOR INSERT
  WITH CHECK (auth.uid() = id);

-- Step 4: Create profiles for users who don't have them
-- Use a more flexible approach that adapts to table structure
DO $$
DECLARE
    user_record RECORD;
    profile_count INTEGER;
    insert_sql TEXT;
    has_email BOOLEAN;
    has_first_name BOOLEAN;
    has_last_name BOOLEAN;
BEGIN
    -- Check table structure again
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' AND column_name = 'email' AND table_schema = 'public'
    ) INTO has_email;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' AND column_name = 'first_name' AND table_schema = 'public'
    ) INTO has_first_name;
    
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' AND column_name = 'last_name' AND table_schema = 'public'
    ) INTO has_last_name;
    
    -- Build INSERT statement based on available columns
    insert_sql := 'INSERT INTO profiles (id, role';
    
    IF has_email THEN
        insert_sql := insert_sql || ', email';
    END IF;
    
    IF has_first_name THEN
        insert_sql := insert_sql || ', first_name';
    END IF;
    
    IF has_last_name THEN
        insert_sql := insert_sql || ', last_name';
    END IF;
    
    insert_sql := insert_sql || ', created_at, updated_at) VALUES ($1, $2';
    
    IF has_email THEN
        insert_sql := insert_sql || ', $3';
    END IF;
    
    IF has_first_name THEN
        insert_sql := insert_sql || ', $4';
    END IF;
    
    IF has_last_name THEN
        insert_sql := insert_sql || ', $5';
    END IF;
    
    insert_sql := insert_sql || ', NOW(), NOW())';
    
    RAISE NOTICE 'Using INSERT SQL: %', insert_sql;
    
    -- Loop through users without profiles
    FOR user_record IN 
        SELECT u.id, u.email, u.raw_user_meta_data
        FROM auth.users u
        LEFT JOIN profiles p ON u.id = p.id
        WHERE p.id IS NULL AND u.email_confirmed_at IS NOT NULL
    LOOP
        RAISE NOTICE 'Creating profile for user: %', user_record.email;
        
        -- Create profile with available columns
        IF has_email AND has_first_name AND has_last_name THEN
            EXECUTE insert_sql USING 
                user_record.id,
                COALESCE((user_record.raw_user_meta_data->>'role')::text, 'student'),
                user_record.email,
                COALESCE((user_record.raw_user_meta_data->>'full_name')::text, split_part(user_record.email, '@', 1)),
                '';
        ELSIF has_email AND has_first_name THEN
            EXECUTE 'INSERT INTO profiles (id, role, email, first_name, created_at, updated_at) VALUES ($1, $2, $3, $4, NOW(), NOW())' USING
                user_record.id,
                COALESCE((user_record.raw_user_meta_data->>'role')::text, 'student'),
                user_record.email,
                COALESCE((user_record.raw_user_meta_data->>'full_name')::text, split_part(user_record.email, '@', 1));
        ELSIF has_email THEN
            EXECUTE 'INSERT INTO profiles (id, role, email, created_at, updated_at) VALUES ($1, $2, $3, NOW(), NOW())' USING
                user_record.id,
                COALESCE((user_record.raw_user_meta_data->>'role')::text, 'student'),
                user_record.email;
        ELSE
            -- Minimal profile with just id and role
            EXECUTE 'INSERT INTO profiles (id, role, created_at, updated_at) VALUES ($1, $2, NOW(), NOW())' USING
                user_record.id,
                COALESCE((user_record.raw_user_meta_data->>'role')::text, 'student');
        END IF;
        
    END LOOP;
END $$;

-- Step 5: Show results
SELECT 
    'Users without profiles' as status,
    COUNT(*) as count
FROM auth.users u
LEFT JOIN profiles p ON u.id = p.id
WHERE p.id IS NULL AND u.email_confirmed_at IS NOT NULL

UNION ALL

SELECT 
    'Users with profiles' as status,
    COUNT(*) as count
FROM auth.users u
INNER JOIN profiles p ON u.id = p.id
WHERE u.email_confirmed_at IS NOT NULL;

-- Step 6: Show current table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'profiles' 
  AND table_schema = 'public'
ORDER BY ordinal_position;
