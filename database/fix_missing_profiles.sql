-- Fix missing profiles and ensure automatic profile creation
-- This script will:
-- 1. Check and fix table structure
-- 2. Fix RLS policies to prevent infinite recursion
-- 3. Create missing profiles for existing users
-- 4. Set up automatic profile creation for new users

-- Step 0: Check current table structure
SELECT
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'profiles'
  AND table_schema = 'public'
ORDER BY ordinal_position;

-- Step 0.5: Add missing columns if they don't exist
ALTER TABLE profiles
  ADD COLUMN IF NOT EXISTS email TEXT,
  ADD COLUMN IF NOT EXISTS first_name TEXT,
  ADD COLUMN IF NOT EXISTS last_name TEXT;

-- Make email NOT NULL after adding it (if it was missing)
UPDATE profiles SET email = '<EMAIL>' WHERE email IS NULL;
-- Don't set NOT NULL constraint yet, we'll do it after populating data

-- Step 1: Fix RLS policies first (prevent infinite recursion)
DROP POLICY IF EXISTS "Tutores podem ver perfis de alunos" ON profiles;

-- Add INSERT policy if it doesn't exist
DROP POLICY IF EXISTS "Usuários podem inserir seus próprios perfis" ON profiles;
CREATE POLICY "Usuários podem inserir seus próprios perfis"
  ON profiles FOR INSERT
  WITH CHECK (auth.uid() = id);

-- Step 2: Create profiles for users who don't have them
-- First, let's see which users are missing profiles
DO $$
DECLARE
    user_record RECORD;
    profile_count INTEGER;
BEGIN
    -- Loop through all users in auth.users
    FOR user_record IN 
        SELECT id, email, raw_user_meta_data
        FROM auth.users 
        WHERE email_confirmed_at IS NOT NULL
    LOOP
        -- Check if profile exists
        SELECT COUNT(*) INTO profile_count
        FROM profiles 
        WHERE id = user_record.id;
        
        -- If no profile exists, create one
        IF profile_count = 0 THEN
            RAISE NOTICE 'Creating profile for user: %', user_record.email;
            
            -- Extract name and role from metadata or use defaults
            INSERT INTO profiles (
                id,
                email,
                first_name,
                last_name,
                role,
                created_at,
                updated_at
            ) VALUES (
                user_record.id,
                user_record.email,
                COALESCE(
                    (user_record.raw_user_meta_data->>'full_name')::text,
                    split_part(user_record.email, '@', 1)
                ),
                '',
                COALESCE(
                    (user_record.raw_user_meta_data->>'role')::text,
                    'student'
                ),
                NOW(),
                NOW()
            );
        END IF;
    END LOOP;
END $$;

-- Step 3: Create a function to automatically create profiles for new users
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (
        id,
        email,
        first_name,
        last_name,
        role,
        created_at,
        updated_at
    ) VALUES (
        NEW.id,
        NEW.email,
        COALESCE(
            (NEW.raw_user_meta_data->>'full_name')::text,
            split_part(NEW.email, '@', 1)
        ),
        '',
        COALESCE(
            (NEW.raw_user_meta_data->>'role')::text,
            'student'
        ),
        NOW(),
        NOW()
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 4: Create trigger to automatically create profiles
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Step 5: Verify the results
SELECT 
    'Users without profiles' as status,
    COUNT(*) as count
FROM auth.users u
LEFT JOIN profiles p ON u.id = p.id
WHERE p.id IS NULL AND u.email_confirmed_at IS NOT NULL

UNION ALL

SELECT 
    'Users with profiles' as status,
    COUNT(*) as count
FROM auth.users u
INNER JOIN profiles p ON u.id = p.id
WHERE u.email_confirmed_at IS NOT NULL

UNION ALL

SELECT 
    'Total confirmed users' as status,
    COUNT(*) as count
FROM auth.users
WHERE email_confirmed_at IS NOT NULL;

-- Step 6: Show all profiles
SELECT 
    p.id,
    p.email,
    p.first_name,
    p.last_name,
    p.role,
    u.email_confirmed_at
FROM profiles p
JOIN auth.users u ON p.id = u.id
ORDER BY p.created_at DESC;
