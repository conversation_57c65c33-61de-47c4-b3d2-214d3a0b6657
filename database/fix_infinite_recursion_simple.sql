-- Simple fix for infinite recursion in RLS policies
-- This approach removes the problematic policy that causes circular dependency
-- and uses a simpler approach for tutor access

-- First, drop the problematic policy that causes infinite recursion
DROP POLICY IF EXISTS "Tutores podem ver perfis de alunos" ON profiles;

-- Instead of trying to check if the current user is a tutor within the profiles policy,
-- we'll create a more permissive policy and handle role-based access in the application layer
-- or use a different approach

-- Option 1: Remove the tutor policy entirely and handle this in application code
-- This is the safest approach to avoid recursion

-- Option 2: Create a simplified policy that doesn't query profiles table
-- We can use the auth.jwt() function to get role from JWT token if it's stored there
-- But this requires the role to be in the JWT claims

-- For now, let's go with Option 1 - remove the problematic policy
-- The application can handle tutor access to student profiles through other means

-- Verify that we still have the basic policies for users to access their own profiles
SELECT
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM
  pg_policies
WHERE
  tablename = 'profiles'
ORDER BY policyname;

-- Also fix other policies that have the same circular dependency issue
-- These policies also query the profiles table to check user role

-- Fix subjects policies
DROP POLICY IF EXISTS "Apenas tutores podem modificar disciplinas" ON subjects;
-- For now, allow all authenticated users to modify subjects
-- You can add role-based restrictions later in application code
CREATE POLICY "Usuários autenticados podem modificar disciplinas"
  ON subjects FOR ALL
  USING (auth.uid() IS NOT NULL);

-- Fix topics policies  
DROP POLICY IF EXISTS "Apenas tutores podem modificar tópicos" ON topics;
CREATE POLICY "Usuários autenticados podem modificar tópicos"
  ON topics FOR ALL
  USING (auth.uid() IS NOT NULL);

-- Fix enem_skills policies
DROP POLICY IF EXISTS "Apenas tutores podem modificar habilidades do ENEM" ON enem_skills;
CREATE POLICY "Usuários autenticados podem modificar habilidades do ENEM"
  ON enem_skills FOR ALL
  USING (auth.uid() IS NOT NULL);

-- Fix questions policies
DROP POLICY IF EXISTS "Apenas tutores podem criar questões" ON questions;
CREATE POLICY "Usuários autenticados podem criar questões"
  ON questions FOR INSERT
  WITH CHECK (auth.uid() IS NOT NULL);

DROP POLICY IF EXISTS "Tutores podem atualizar questões que criaram" ON questions;
CREATE POLICY "Usuários podem atualizar questões que criaram"
  ON questions FOR UPDATE
  USING (created_by_tutor_id = auth.uid() OR created_by_tutor_id IS NULL);

-- Fix student_answers policies
DROP POLICY IF EXISTS "Estudantes veem apenas suas próprias respostas" ON student_answers;
CREATE POLICY "Usuários veem suas próprias respostas"
  ON student_answers FOR SELECT
  USING (student_id = auth.uid());

DROP POLICY IF EXISTS "Estudantes podem inserir apenas suas próprias respostas" ON student_answers;
CREATE POLICY "Usuários podem inserir suas próprias respostas"
  ON student_answers FOR INSERT
  WITH CHECK (student_id = auth.uid());

-- Fix student_interactions policies
DROP POLICY IF EXISTS "Estudantes veem apenas suas próprias interações" ON student_interactions;
CREATE POLICY "Usuários veem suas próprias interações"
  ON student_interactions FOR SELECT
  USING (student_id = auth.uid());

DROP POLICY IF EXISTS "Estudantes podem inserir apenas suas próprias interações" ON student_interactions;
CREATE POLICY "Usuários podem inserir suas próprias interações"
  ON student_interactions FOR INSERT
  WITH CHECK (student_id = auth.uid());

-- Show final policies
SELECT
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM
  pg_policies
WHERE
  tablename IN ('profiles', 'subjects', 'topics', 'enem_skills', 'questions', 'student_answers', 'student_interactions')
ORDER BY tablename, policyname;
