-- Fix infinite recursion in RLS policies for profiles table
-- The issue is that policies are trying to query profiles table to check user role,
-- which creates circular dependency

-- First, drop the problematic policy that causes infinite recursion
DROP POLICY IF EXISTS "Tutores podem ver perfis de alunos" ON profiles;

-- Create a security definer function to get user role without triggering RLS
CREATE OR REPLACE FUNCTION get_user_role(user_id UUID)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  user_role TEXT;
BEGIN
  SELECT role INTO user_role
  FROM profiles
  WHERE id = user_id;
  
  RETURN user_role;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_user_role(UUID) TO authenticated;

-- Recreate the tutor policy using the security definer function
CREATE POLICY "Tutores podem ver perfis de alunos"
  ON profiles FOR SELECT
  USING (
    get_user_role(auth.uid()) = 'tutor' 
    AND role = 'student'
  );

-- Also need to fix other policies that have the same issue
-- Drop and recreate policies that query profiles table

-- Fix subjects policies
DROP POLICY IF EXISTS "Apenas tutores podem modificar disciplinas" ON subjects;
CREATE POLICY "Apenas tutores podem modificar disciplinas"
  ON subjects FOR ALL
  USING (get_user_role(auth.uid()) = 'tutor');

-- Fix topics policies  
DROP POLICY IF EXISTS "Apenas tutores podem modificar tópicos" ON topics;
CREATE POLICY "Apenas tutores podem modificar tópicos"
  ON topics FOR ALL
  USING (get_user_role(auth.uid()) = 'tutor');

-- Fix enem_skills policies
DROP POLICY IF EXISTS "Apenas tutores podem modificar habilidades do ENEM" ON enem_skills;
CREATE POLICY "Apenas tutores podem modificar habilidades do ENEM"
  ON enem_skills FOR ALL
  USING (get_user_role(auth.uid()) = 'tutor');

-- Fix questions policies
DROP POLICY IF EXISTS "Apenas tutores podem criar questões" ON questions;
CREATE POLICY "Apenas tutores podem criar questões"
  ON questions FOR INSERT
  WITH CHECK (get_user_role(auth.uid()) = 'tutor');

DROP POLICY IF EXISTS "Tutores podem atualizar questões que criaram" ON questions;
CREATE POLICY "Tutores podem atualizar questões que criaram"
  ON questions FOR UPDATE
  USING (
    get_user_role(auth.uid()) = 'tutor' 
    AND (created_by_tutor_id = auth.uid() OR created_by_tutor_id IS NULL)
  );

-- Fix student_answers policies
DROP POLICY IF EXISTS "Estudantes veem apenas suas próprias respostas" ON student_answers;
CREATE POLICY "Estudantes veem apenas suas próprias respostas"
  ON student_answers FOR SELECT
  USING (
    student_id = auth.uid() 
    OR get_user_role(auth.uid()) = 'tutor'
  );

DROP POLICY IF EXISTS "Estudantes podem inserir apenas suas próprias respostas" ON student_answers;
CREATE POLICY "Estudantes podem inserir apenas suas próprias respostas"
  ON student_answers FOR INSERT
  WITH CHECK (
    student_id = auth.uid() 
    AND get_user_role(auth.uid()) = 'student'
  );

-- Fix student_interactions policies
DROP POLICY IF EXISTS "Estudantes veem apenas suas próprias interações" ON student_interactions;
CREATE POLICY "Estudantes veem apenas suas próprias interações"
  ON student_interactions FOR SELECT
  USING (
    student_id = auth.uid() 
    OR get_user_role(auth.uid()) = 'tutor'
  );

DROP POLICY IF EXISTS "Estudantes podem inserir apenas suas próprias interações" ON student_interactions;
CREATE POLICY "Estudantes podem inserir apenas suas próprias interações"
  ON student_interactions FOR INSERT
  WITH CHECK (
    student_id = auth.uid() 
    AND get_user_role(auth.uid()) = 'student'
  );

-- Verify the policies are correctly applied
SELECT
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual,
  with_check
FROM
  pg_policies
WHERE
  tablename IN ('profiles', 'subjects', 'topics', 'enem_skills', 'questions', 'student_answers', 'student_interactions')
ORDER BY tablename, policyname;
