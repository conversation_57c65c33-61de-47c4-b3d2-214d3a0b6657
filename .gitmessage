# <type>(<scope>): <subject>
#
# <body>
#
# <footer>

# Type should be one of the following:
# * feat: A new feature
# * fix: A bug fix
# * docs: Documentation only changes
# * style: Changes that do not affect the meaning of the code
# * refactor: A code change that neither fixes a bug nor adds a feature
# * perf: A code change that improves performance
# * test: Adding missing tests or correcting existing tests
# * build: Changes that affect the build system or external dependencies
# * ci: Changes to our CI configuration files and scripts
# * chore: Other changes that don't modify src or test files
# * revert: Reverts a previous commit
#
# Scope is optional and should be the name of the package affected
#
# Subject should use imperative mood and not end with period
# No more than 50 characters
#
# Body should include motivation for the change and contrast with previous behavior
# Wrap at 72 characters
#
# Footer should contain any information about Breaking Changes
# and is also the place to reference GitHub issues that this commit closes
#
# Example:
# feat(auth): add OAuth2 integration
#
# Implement OAuth2 authentication flow using Google provider.
# This allows users to sign in with their Google accounts.
#
# Closes #123
# BREAKING CHANGE: Authentication API has changed
