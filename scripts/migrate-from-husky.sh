#!/bin/bash

# Migration script from Husky to modern Git workflow
# This script helps transition from deprecated <PERSON>sky to simple-git-hooks

set -e

echo "🚀 Migrating from Husky to modern Git workflow..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in a git repository
if [ ! -d ".git" ]; then
    print_error "This script must be run from the root of a Git repository"
    exit 1
fi

print_status "Starting migration process..."

# 1. Remove old <PERSON>sky hooks
if [ -d ".husky" ]; then
    print_status "Removing old Husky configuration..."
    rm -rf .husky
    print_success "Removed .husky directory"
else
    print_warning ".husky directory not found, skipping removal"
fi

# 2. Uninstall Husky if it exists in package.json
if command -v npm &> /dev/null; then
    if npm list husky &> /dev/null; then
        print_status "Uninstalling Husky..."
        npm uninstall husky
        print_success "Husky uninstalled"
    else
        print_warning "Husky not found in dependencies, skipping uninstall"
    fi
else
    print_error "npm not found. Please uninstall husky manually"
fi

# 3. Install new dependencies
print_status "Installing new dependencies..."
npm install

# 4. Initialize simple-git-hooks
print_status "Initializing simple-git-hooks..."
npm run prepare

# 5. Set up Git commit template (optional)
read -p "Do you want to set up the Git commit message template? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    git config commit.template .gitmessage
    print_success "Git commit template configured"
fi

# 6. Verify installation
print_status "Verifying installation..."

# Check if git hooks are installed
if [ -f ".git/hooks/pre-commit" ]; then
    print_success "Pre-commit hook installed"
else
    print_error "Pre-commit hook not found"
fi

if [ -f ".git/hooks/commit-msg" ]; then
    print_success "Commit-msg hook installed"
else
    print_error "Commit-msg hook not found"
fi

# 7. Test the setup
print_status "Testing the setup..."

# Create a test file to verify pre-commit hooks
echo "// Test file for pre-commit hooks" > test-file.js
git add test-file.js

print_status "Running pre-commit hooks test..."
if npm run validate; then
    print_success "Pre-commit hooks are working correctly"
else
    print_warning "Pre-commit hooks test failed, but this might be expected"
fi

# Clean up test file
git reset HEAD test-file.js
rm -f test-file.js

print_success "Migration completed successfully!"

echo ""
echo "📋 Next steps:"
echo "1. Review the new Git workflow documentation: docs/git-workflow.md"
echo "2. Use 'npm run commit' for guided commit messages"
echo "3. Follow conventional commit format for all commits"
echo "4. Set up GitHub repository secrets for automated releases:"
echo "   - GITHUB_TOKEN (automatically available)"
echo "   - NPM_TOKEN (if publishing to npm)"
echo ""
echo "🎉 Your project is now using modern Git workflow practices!"
echo "📖 Read docs/git-workflow.md for detailed usage instructions"
