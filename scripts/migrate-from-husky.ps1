# Migration script from <PERSON>sky to modern Git workflow (PowerShell version)
# This script helps transition from deprecated <PERSON><PERSON> to simple-git-hooks

param(
    [switch]$Force
)

# Colors for output
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    White = "White"
}

function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor $Colors.Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor $Colors.Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor $Colors.Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor $Colors.Red
}

Write-Host "🚀 Migrating from Husky to modern Git workflow..." -ForegroundColor $Colors.Blue

# Check if we're in a git repository
if (-not (Test-Path ".git")) {
    Write-Error "This script must be run from the root of a Git repository"
    exit 1
}

Write-Status "Starting migration process..."

# 1. Remove old <PERSON>sky hooks
if (Test-Path ".husky") {
    Write-Status "Removing old Husky configuration..."
    Remove-Item -Recurse -Force ".husky"
    Write-Success "Removed .husky directory"
} else {
    Write-Warning ".husky directory not found, skipping removal"
}

# 2. Check if npm is available
if (-not (Get-Command npm -ErrorAction SilentlyContinue)) {
    Write-Error "npm not found. Please install Node.js and npm first."
    exit 1
}

# 3. Uninstall Husky if it exists
try {
    $huskyInstalled = npm list husky 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Status "Uninstalling Husky..."
        npm uninstall husky
        Write-Success "Husky uninstalled"
    } else {
        Write-Warning "Husky not found in dependencies, skipping uninstall"
    }
} catch {
    Write-Warning "Could not check for Husky installation"
}

# 4. Install new dependencies
Write-Status "Installing new dependencies..."
npm install

if ($LASTEXITCODE -ne 0) {
    Write-Error "Failed to install dependencies"
    exit 1
}

# 5. Initialize simple-git-hooks
Write-Status "Initializing simple-git-hooks..."
npm run prepare

if ($LASTEXITCODE -ne 0) {
    Write-Error "Failed to initialize simple-git-hooks"
    exit 1
}

# 6. Set up Git commit template (optional)
if (-not $Force) {
    $response = Read-Host "Do you want to set up the Git commit message template? (y/n)"
    if ($response -match "^[Yy]$") {
        git config commit.template .gitmessage
        Write-Success "Git commit template configured"
    }
}

# 7. Verify installation
Write-Status "Verifying installation..."

# Check if git hooks are installed
if (Test-Path ".git/hooks/pre-commit") {
    Write-Success "Pre-commit hook installed"
} else {
    Write-Error "Pre-commit hook not found"
}

if (Test-Path ".git/hooks/commit-msg") {
    Write-Success "Commit-msg hook installed"
} else {
    Write-Error "Commit-msg hook not found"
}

# 8. Test the setup
Write-Status "Testing the setup..."

# Create a test file to verify pre-commit hooks
"// Test file for pre-commit hooks" | Out-File -FilePath "test-file.js" -Encoding UTF8
git add test-file.js

Write-Status "Running pre-commit hooks test..."
npm run validate

if ($LASTEXITCODE -eq 0) {
    Write-Success "Pre-commit hooks are working correctly"
} else {
    Write-Warning "Pre-commit hooks test failed, but this might be expected"
}

# Clean up test file
git reset HEAD test-file.js
Remove-Item "test-file.js" -ErrorAction SilentlyContinue

Write-Success "Migration completed successfully!"

Write-Host ""
Write-Host "📋 Next steps:" -ForegroundColor $Colors.White
Write-Host "1. Review the new Git workflow documentation: docs/git-workflow.md"
Write-Host "2. Use 'npm run commit' for guided commit messages"
Write-Host "3. Follow conventional commit format for all commits"
Write-Host "4. Set up GitHub repository secrets for automated releases:"
Write-Host "   - GITHUB_TOKEN (automatically available)"
Write-Host "   - NPM_TOKEN (if publishing to npm)"
Write-Host ""
Write-Host "🎉 Your project is now using modern Git workflow practices!" -ForegroundColor $Colors.Green
Write-Host "📖 Read docs/git-workflow.md for detailed usage instructions" -ForegroundColor $Colors.Blue
