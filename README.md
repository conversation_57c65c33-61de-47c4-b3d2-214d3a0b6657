# Gemnial Learning Platform

Uma plataforma moderna de e-learning construída com Next.js, TypeScript, Tailwind CSS e Supabase.

## Funcionalidades

- 🚀 Next.js 13+ com App Router
- 🎨 Estilizado com Tailwind CSS
- 🔍 TypeScript para segurança de tipos
- 🛠 ESLint e Prettier para qualidade de código
- 🐶 Husky para Git hooks
- 🔐 Autenticação completa com Supabase
- 👥 Suporte a múltiplos perfis (estudante e tutor)
- 🛡️ Proteção de rotas baseada em funções

## Começando

### Pré-requisitos

- Node.js 18.0.0 ou posterior
- npm ou yarn como gerenciador de pacotes
- Conta no [Supabase](https://supabase.com)

### Configuração do Supabase

1. Crie um novo projeto no [Supabase](https://app.supabase.com)

2. Configure a autenticação:

   - Vá para Authentication > Settings
   - Habilite Email/Password sign-in
   - Configure o Email template conforme necessário

3. Configure o banco de dados:

   - Execute o seguinte SQL no SQL Editor para criar a tabela de perfis:

   ```sql
   -- Criar tabela de perfis
   CREATE TABLE profiles (
     id UUID REFERENCES auth.users(id) PRIMARY KEY,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     email TEXT NOT NULL,
     first_name TEXT,
     last_name TEXT,
     avatar_url TEXT,
     role TEXT NOT NULL CHECK (role IN ('student', 'tutor'))
   );

   -- Configurar RLS (Row Level Security)
   ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

   -- Criar políticas de acesso
   CREATE POLICY "Usuários podem ver seus próprios perfis"
     ON profiles FOR SELECT
     USING (auth.uid() = id);

   CREATE POLICY "Usuários podem atualizar seus próprios perfis"
     ON profiles FOR UPDATE
     USING (auth.uid() = id);
   ```

4. Obtenha as credenciais do projeto:
   - Vá para Project Settings > API
   - Copie a URL do projeto e a anon key (chave anônima)

### Instalação

1. Clone o repositório:

   ```bash
   git clone https://github.com/seu-usuario/gemnial-platform.git
   cd gemnial-platform
   ```

2. Instale as dependências:

   ```bash
   npm install
   # ou
   yarn
   ```

3. Configure as variáveis de ambiente:

   - Copie o arquivo `.env.example` para `.env.local`
   - Preencha as variáveis com as credenciais do Supabase:

   ```
   NEXT_PUBLIC_SUPABASE_URL=sua_url_do_supabase
   NEXT_PUBLIC_SUPABASE_ANON_KEY=sua_chave_anonima_do_supabase
   ```

4. Inicie o servidor de desenvolvimento:

   ```bash
   npm run dev
   # ou
   yarn dev
   ```

5. Abra [http://localhost:3000](http://localhost:3000) no seu navegador.

## Estrutura do Projeto

```
gemnial-platform/
├── app/                    # App router
│   ├── auth/               # Páginas de autenticação
│   │   ├── login/          # Página de login
│   │   └── signup/         # Página de cadastro
│   ├── (student)/          # Rotas para estudantes (protegidas)
│   │   ├── dashboard/      # Dashboard do estudante
│   │   └── layout.tsx      # Layout compartilhado para estudantes
│   ├── (tutor)/            # Rotas para tutores (protegidas)
│   │   ├── dashboard/      # Dashboard do tutor
│   │   └── layout.tsx      # Layout compartilhado para tutores
│   ├── layout.tsx          # Layout raiz com AuthProvider
│   └── page.tsx            # Página inicial
├── components/             # Componentes reutilizáveis
│   ├── auth/               # Componentes de autenticação
│   │   └── AuthForm.tsx    # Formulário de autenticação reutilizável
│   ├── common/             # Componentes comuns
│   │   ├── UserNav.tsx     # Navegação do usuário
│   │   └── LoadingSpinner.tsx # Indicador de carregamento
│   ├── ui/                 # Componentes de UI
│   └── features/           # Componentes específicos de features
├── lib/                    # Código de biblioteca
│   ├── auth.tsx            # Contexto de autenticação e HOC withAuth
│   ├── supabaseClient.ts   # Cliente Supabase
│   └── database.types.ts   # Tipos do banco de dados Supabase
├── middleware.ts           # Middleware para proteção de rotas
├── public/                 # Arquivos estáticos
├── services/               # Serviços de API
└── utils/                  # Funções utilitárias
```

## Scripts Disponíveis

- `npm run dev` - Inicia o servidor de desenvolvimento
- `npm run build` - Compila o projeto para produção
- `npm start` - Inicia o servidor de produção
- `npm run lint` - Executa o ESLint
- `npm run format` - Formata o código com Prettier
- `npm run check-types` - Verifica os tipos TypeScript

## Autenticação

O sistema de autenticação implementado oferece:

1. **Login e Cadastro**: Autenticação completa com email/senha através do Supabase Auth.

2. **Perfis de Usuário**: Suporte para dois tipos de perfis:

   - **Estudante**: Acesso às rotas `/student/*`
   - **Tutor**: Acesso às rotas `/tutor/*`

3. **Proteção de Rotas**: Implementada em três níveis:

   - **Middleware**: Verificação no lado do servidor para rotas protegidas
   - **AuthProvider**: Contexto React para gerenciar o estado de autenticação
   - **HOC withAuth**: Componente de ordem superior para proteger componentes individuais

4. **Gerenciamento de Sessão**: Sessões persistentes gerenciadas pelo Supabase.

## Code Quality

This project uses:

- ESLint for code linting
- Prettier for code formatting
- Husky for Git hooks
- Lint-staged for running linters on Git staged files

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
