# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Local development
.cache
.temp
.tmp

# Misc
*.p12
*.key
*.crt
*.cer

# Local Netlify folder if it exists
.netlify

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.test
.env.production

# parcel-bundler cache (https://parceljs.org/)
.parcel-cache

# Next.js build output
.next

# Local env files
.env*.local
.env.development.local
.env.test.local
.env.production.local

# Git hooks (managed by simple-git-hooks)
.git/hooks/*
!.git/hooks/pre-commit
!.git/hooks/commit-msg

# Semantic release
.semantic-release/

# Coverage reports
coverage/
*.lcov

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity
