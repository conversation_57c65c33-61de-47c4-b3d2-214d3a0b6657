{"name": "gemnial-platform", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "lint:fix": "eslint --fix --ext .ts,.tsx .", "check-types": "tsc --noEmit", "prepare": "simple-git-hooks", "test": "echo \"No tests specified\" && exit 0", "validate": "npm run check-types && npm run lint && npm run build", "commit": "git-cz", "release": "semantic-release"}, "lint-staged": {"*.{js,jsx,ts,tsx,json,css,md}": ["prettier --write"], "*.{js,jsx,ts,tsx}": ["eslint --fix"]}, "simple-git-hooks": {"pre-commit": "npx lint-staged", "commit-msg": "npx commitlint --edit $1"}, "config": {"commitizen": {"path": "@commitlint/cz-commitlint"}}, "dependencies": {"@headlessui/react": "^2.2.4", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.8", "class-variance-authority": "^0.7.1", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@commitlint/cli": "^19.6.0", "@commitlint/config-conventional": "^19.6.0", "@commitlint/cz-commitlint": "^19.6.0", "@eslint/eslintrc": "^3", "@semantic-release/changelog": "^6.0.3", "@semantic-release/git": "^10.0.1", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "commitizen": "^4.3.1", "eslint": "^9", "eslint-config-next": "15.3.2", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "lint-staged": "^16.1.0", "prettier": "^3.5.3", "semantic-release": "^22.0.12", "simple-git-hooks": "^2.12.0", "tailwindcss": "^4", "typescript": "^5"}}