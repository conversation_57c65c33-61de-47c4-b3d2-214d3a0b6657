'use client';

import { useState } from 'react';

interface CopyButtonProps {
  text: string;
  className?: string;
}

export default function CopyButton({ text, className = '' }: CopyButtonProps) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      // Verificar se a API Clipboard está disponível
      if (!navigator.clipboard) {
        // Fallback para método alternativo
        const textArea = document.createElement('textarea');
        textArea.value = text;

        // Evitar rolagem
        textArea.style.top = '0';
        textArea.style.left = '0';
        textArea.style.position = 'fixed';

        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
          const successful = document.execCommand('copy');
          if (!successful) throw new Error('Falha ao copiar texto');
          setCopied(true);
        } catch (err) {
          console.error('Erro ao copiar:', err);
        }

        document.body.removeChild(textArea);
      } else {
        // Usar a API Clipboard moderna
        await navigator.clipboard.writeText(text);
        setCopied(true);
      }

      // Resetar o estado após 2 segundos
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Erro ao copiar para a área de transferência:', err);
    }
  };

  return (
    <button
      onClick={handleCopy}
      className={`px-3 py-1 text-sm rounded-md transition-colors ${
        copied ? 'bg-green-100 text-green-800' : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
      } ${className}`}
    >
      {copied ? 'Copiado!' : 'Copiar'}
    </button>
  );
}
