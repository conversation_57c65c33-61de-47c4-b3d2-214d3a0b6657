'use client';

import React, { Fragment } from 'react';
import { Menu, Transition } from '@headlessui/react';

export interface DropdownItem {
  id: string;
  label: React.ReactNode;
  onClick?: () => void;
  icon?: React.ReactNode;
  disabled?: boolean;
  href?: string;
  divider?: boolean;
}

export interface DropdownProps {
  trigger: React.ReactNode;
  items: DropdownItem[];
  align?: 'left' | 'right';
  width?: number | string;
  className?: string;
}

const Dropdown: React.FC<DropdownProps> = ({
  trigger,
  items,
  align = 'right',
  width = 'auto',
  className = '',
}) => {
  const alignmentClasses = {
    left: 'left-0 origin-top-left',
    right: 'right-0 origin-top-right',
  };

  return (
    <Menu as="div" className={`relative inline-block text-left ${className}`}>
      <Menu.Button as={React.Fragment}>{trigger}</Menu.Button>

      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="transform opacity-0 scale-95"
        enterTo="transform opacity-100 scale-100"
        leave="transition ease-in duration-75"
        leaveFrom="transform opacity-100 scale-100"
        leaveTo="transform opacity-0 scale-95"
      >
        <Menu.Items
          className={`absolute z-10 mt-2 ${alignmentClasses[align]} bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none`}
          style={{ width: width }}
        >
          <div className="py-1">
            {items.map((item, index) => (
              <React.Fragment key={item.id || index}>
                {item.divider && <hr className="my-1 border-gray-200" />}
                <Menu.Item disabled={item.disabled}>
                  {({ active }) => {
                    const Component = item.href ? 'a' : 'button';
                    const props = item.href
                      ? { href: item.href }
                      : { type: 'button' as const, onClick: item.onClick };

                    return (
                      <Component
                        {...props}
                        className={`${active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'} ${
                          item.disabled ? 'opacity-50 cursor-not-allowed' : ''
                        } group flex w-full items-center px-4 py-2 text-sm`}
                        disabled={item.disabled}
                      >
                        {item.icon && (
                          <span className="mr-3 h-5 w-5 text-gray-400 group-hover:text-gray-500">
                            {item.icon}
                          </span>
                        )}
                        {item.label}
                      </Component>
                    );
                  }}
                </Menu.Item>
              </React.Fragment>
            ))}
          </div>
        </Menu.Items>
      </Transition>
    </Menu>
  );
};

export { Dropdown };
