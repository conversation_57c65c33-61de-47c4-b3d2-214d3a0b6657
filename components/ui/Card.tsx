import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';

// Definindo as variantes do card usando class-variance-authority
const cardVariants = cva(
  // Base styles
  'rounded-lg overflow-hidden',
  {
    variants: {
      variant: {
        default: 'bg-white border border-gray-200 shadow',
        elevated: 'bg-white shadow-md',
        outlined: 'bg-white border border-gray-200',
        flat: 'bg-gray-50',
      },
      padding: {
        none: '',
        sm: 'p-3',
        md: 'p-5',
        lg: 'p-7',
      },
    },
    defaultVariants: {
      variant: 'default',
      padding: 'md',
    },
  }
);

// Tipos para as props do card
export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  as?: React.ElementType;
}

// Componente Card
const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant, padding, as: Component = 'div', children, ...props }, ref) => {
    return (
      <Component className={cardVariants({ variant, padding, className })} ref={ref} {...props}>
        {children}
      </Component>
    );
  }
);

// Componente CardHeader
const CardHeader = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, children, ...props }, ref) => {
    return (
      <div className={`px-5 py-4 border-b border-gray-200 ${className || ''}`} ref={ref} {...props}>
        {children}
      </div>
    );
  }
);

// Componente CardTitle
const CardTitle = React.forwardRef<HTMLHeadingElement, React.HTMLAttributes<HTMLHeadingElement>>(
  ({ className, children, ...props }, ref) => {
    return (
      <h3 className={`text-lg font-medium text-gray-900 ${className || ''}`} ref={ref} {...props}>
        {children}
      </h3>
    );
  }
);

// Componente CardDescription
const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, children, ...props }, ref) => {
  return (
    <p className={`mt-1 text-sm text-gray-500 ${className || ''}`} ref={ref} {...props}>
      {children}
    </p>
  );
});

// Componente CardContent
const CardContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, children, ...props }, ref) => {
    return (
      <div className={`px-5 py-4 ${className || ''}`} ref={ref} {...props}>
        {children}
      </div>
    );
  }
);

// Componente CardFooter
const CardFooter = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, children, ...props }, ref) => {
    return (
      <div className={`px-5 py-4 border-t border-gray-200 ${className || ''}`} ref={ref} {...props}>
        {children}
      </div>
    );
  }
);

Card.displayName = 'Card';
CardHeader.displayName = 'CardHeader';
CardTitle.displayName = 'CardTitle';
CardDescription.displayName = 'CardDescription';
CardContent.displayName = 'CardContent';
CardFooter.displayName = 'CardFooter';

export { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter };
