'use client';

import { createContext, useContext, useEffect, useState, ReactNode, useCallback } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { createBrowserSupabaseClient } from './supabaseClient';
import { User, Session } from '@supabase/supabase-js';

// Definição do tipo para o perfil do usuário
type UserProfile = {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: 'student' | 'tutor';
  created_at?: string;
  updated_at?: string;
};

// Definição do tipo para o contexto de autenticação
type AuthContextType = {
  user: User | null;
  session: Session | null;
  profile: UserProfile | null;
  isLoading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, name: string, role: string) => Promise<void>;
  signOut: () => Promise<void>;
};

// Criação do contexto de autenticação
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Hook personalizado para usar o contexto de autenticação
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider');
  }
  return context;
}

// Provedor de autenticação
export function AuthProvider({ children }: { children: ReactNode }) {
  const [supabaseClient] = useState(() => createBrowserSupabaseClient());
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const pathname = usePathname();

  // Função para criar perfil básico para usuário sem perfil
  const createMissingProfile = useCallback(
    async (userId: string) => {
      try {
        // Buscar dados do usuário autenticado
        const {
          data: { user },
        } = await supabaseClient.auth.getUser();

        if (!user || user.id !== userId) {
          console.error('Usuário não encontrado ou ID não confere');
          return null;
        }

        const email = user.email || '';
        const fullName = user.user_metadata?.full_name || email.split('@')[0];
        const role = user.user_metadata?.role || 'student';

        console.log('Criando perfil básico para:', email);

        const { data, error } = await supabaseClient
          .from('profiles')
          .insert({
            id: userId,
            email: email,
            first_name: fullName.split(' ')[0] || fullName,
            last_name: fullName.split(' ').slice(1).join(' ') || '',
            role: role as 'student' | 'tutor',
          })
          .select()
          .single();

        if (error) {
          console.error('Erro ao criar perfil básico:', error);
          return null;
        }

        console.log('Perfil básico criado com sucesso:', data);
        return data;
      } catch (error) {
        console.error('Exceção ao criar perfil básico:', error);
        return null;
      }
    },
    [supabaseClient]
  );

  // Função para buscar o perfil do usuário
  const fetchProfile = useCallback(
    async (userId: string, retryCount = 0) => {
      try {
        // Verificar se temos um userId válido
        if (!userId) {
          console.error('ID de usuário inválido ao buscar perfil');
          return null;
        }

        // Verificar se o cliente Supabase está inicializado
        if (!supabaseClient) {
          console.error('Cliente Supabase não inicializado');
          return null;
        }

        // Buscar o perfil com tratamento de erro mais detalhado
        const { data, error } = await supabaseClient
          .from('profiles')
          .select('*')
          .eq('id', userId)
          .single();

        if (error) {
          console.error('Erro ao buscar perfil:', error.message, error.details, error.hint);

          // Se for erro de recursão infinita e ainda não tentamos muitas vezes, aguardar e tentar novamente
          if (error.message.includes('infinite recursion') && retryCount < 3) {
            console.log(`Tentativa ${retryCount + 1} de buscar perfil após erro de recursão...`);
            await new Promise((resolve) => setTimeout(resolve, 1000 * (retryCount + 1)));
            return fetchProfile(userId, retryCount + 1);
          }

          // Se o perfil não existe (0 rows returned), tentar criar um perfil básico
          if (error.message.includes('0 rows') || error.message.includes('no rows')) {
            console.log('Perfil não encontrado, tentando criar perfil básico...');
            return await createMissingProfile(userId);
          }

          // Para outros erros, verificar se o perfil existe
          if (!error.message.includes('infinite recursion')) {
            const { count, error: countError } = await supabaseClient
              .from('profiles')
              .select('*', { count: 'exact', head: true })
              .eq('id', userId);

            if (countError) {
              console.error('Erro ao verificar existência do perfil:', countError);
            } else if (count === 0) {
              console.error('Perfil não encontrado para o usuário:', userId);
              return await createMissingProfile(userId);
            }
          }

          return null;
        }

        return data;
      } catch (error) {
        console.error('Exceção ao buscar perfil:', error);
        return null;
      }
    },
    [supabaseClient, createMissingProfile]
  );

  // Efeito para verificar a sessão atual e configurar o usuário
  useEffect(() => {
    const setUpUser = async () => {
      try {
        const {
          data: { session: currentSession },
        } = await supabaseClient.auth.getSession();

        if (currentSession) {
          setSession(currentSession);
          setUser(currentSession.user);

          // Buscar o perfil do usuário
          const userProfile = await fetchProfile(currentSession.user.id);
          setProfile(userProfile);
        }
      } catch (error) {
        console.error('Erro ao configurar usuário:', error);
      } finally {
        setIsLoading(false);
      }
    };

    setUpUser();

    // Configurar o listener para mudanças de autenticação
    const { data: authListener } = supabaseClient.auth.onAuthStateChange(
      async (event, newSession) => {
        console.log('Auth state changed:', event, newSession?.user?.id);
        setSession(newSession);
        setUser(newSession?.user ?? null);

        if (newSession?.user) {
          const userProfile = await fetchProfile(newSession.user.id);
          setProfile(userProfile);

          // Redirecionar após login bem-sucedido
          if (event === 'SIGNED_IN' && userProfile) {
            const currentPath = pathname;
            const isAuthPage = currentPath.startsWith('/auth/');

            console.log('Login bem-sucedido:', {
              event,
              userProfile: userProfile,
              currentPath,
              isAuthPage,
              role: userProfile.role,
            });

            if (isAuthPage) {
              console.log('Redirecionando usuário...');
              if (userProfile.role === 'student') {
                console.log('Redirecionando para dashboard do estudante');
                router.push('/student/dashboard');
              } else if (userProfile.role === 'tutor') {
                console.log('Redirecionando para dashboard do tutor');
                router.push('/tutor/dashboard');
              } else {
                console.log('Role não reconhecido, redirecionando para home');
                router.push('/');
              }
            } else {
              console.log('Não está em página de auth, não redirecionando');
            }
          }
        } else {
          setProfile(null);
        }

        setIsLoading(false);
      }
    );

    return () => {
      authListener.subscription.unsubscribe();
    };
  }, [supabaseClient, fetchProfile, pathname, router]);

  // Efeito para redirecionar com base no estado de autenticação e na rota atual
  useEffect(() => {
    if (isLoading) return;

    // Verificar se o usuário está tentando acessar uma rota protegida
    const isAccessingProtectedRoute =
      pathname?.startsWith('/student/') || pathname?.startsWith('/tutor/');

    // Verificar se o usuário está tentando acessar uma rota específica de função
    const isAccessingStudentRoute = pathname?.startsWith('/student/');
    const isAccessingTutorRoute = pathname?.startsWith('/tutor/');
    const isAccessingAuthRoute = pathname?.startsWith('/auth/');
    const isAccessingHomePage = pathname === '/';

    console.log('AuthProvider useEffect:', {
      pathname,
      user: !!user,
      profile: profile ? { role: profile.role, id: profile.id } : null,
      isLoading,
      isAccessingStudentRoute,
      isAccessingTutorRoute,
      isAccessingProtectedRoute,
      isAccessingAuthRoute,
      isAccessingHomePage,
    });

    if (!user && isAccessingProtectedRoute) {
      // Redirecionar para login se não estiver autenticado
      console.log('Redirecionando para login - usuário não autenticado');
      router.push('/auth/login');
    } else if (user && profile) {
      // Verificar se o usuário está tentando acessar uma rota que não corresponde à sua função
      if (
        (isAccessingStudentRoute && profile.role !== 'student') ||
        (isAccessingTutorRoute && profile.role !== 'tutor')
      ) {
        // Redirecionar para a dashboard correta com base na função
        console.log('Redirecionando para dashboard correta - role mismatch');
        if (profile.role === 'student') {
          router.push('/student/dashboard');
        } else if (profile.role === 'tutor') {
          router.push('/tutor/dashboard');
        }
      }

      // Se usuário autenticado está na home page, redirecionar para dashboard
      if (isAccessingHomePage) {
        console.log('Usuário autenticado na home, redirecionando para dashboard');
        if (profile.role === 'student') {
          router.push('/student/dashboard');
        } else if (profile.role === 'tutor') {
          router.push('/tutor/dashboard');
        }
      }
    }
  }, [user, profile, pathname, isLoading, router]);

  // Funções de autenticação
  const signIn = async (email: string, password: string) => {
    try {
      const { error } = await supabaseClient.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw error;
      }
    } catch (error) {
      throw error;
    }
  };

  const signUp = async (email: string, password: string, name: string, role: string) => {
    try {
      const { data, error } = await supabaseClient.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: name,
            role,
          },
        },
      });

      if (error) {
        throw error;
      }

      if (data.user) {
        // Criar perfil do usuário
        const { error: profileError } = await supabaseClient.from('profiles').insert({
          id: data.user.id,
          email,
          first_name: name.split(' ')[0],
          last_name: name.split(' ').slice(1).join(' '),
          role: role as 'student' | 'tutor',
        });

        if (profileError) {
          throw profileError;
        }
      }
    } catch (error) {
      throw error;
    }
  };

  const signOut = async () => {
    try {
      const { error } = await supabaseClient.auth.signOut();
      if (error) {
        throw error;
      }
      router.push('/');
    } catch (error) {
      throw error;
    }
  };

  const value = {
    user,
    session,
    profile,
    isLoading,
    signIn,
    signUp,
    signOut,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// HOC para proteger rotas
export function withAuth<P extends object>(Component: React.ComponentType<P>): React.FC<P> {
  const WithAuth: React.FC<P> = (props) => {
    const { user, isLoading } = useAuth();
    const router = useRouter();

    useEffect(() => {
      if (!isLoading && !user) {
        router.push('/auth/login');
      }
    }, [user, isLoading, router]);

    if (isLoading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      );
    }

    if (!user) {
      return null;
    }

    return <Component {...props} />;
  };

  return WithAuth;
}
