export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[];

export type QuestionStatus = 'pending_validation' | 'validated' | 'rejected' | 'ai_generated';
export type UserRole = 'student' | 'tutor';

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          role: UserRole;
          email: string;
          first_name: string | null;
          last_name: string | null;
          avatar_url: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          role: UserRole;
          email: string;
          first_name?: string | null;
          last_name?: string | null;
          avatar_url?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          role?: UserRole;
          email?: string;
          first_name?: string | null;
          last_name?: string | null;
          avatar_url?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      subjects: {
        Row: {
          id: number;
          name: string;
        };
        Insert: {
          id?: number;
          name: string;
        };
        Update: {
          id?: number;
          name?: string;
        };
      };
      topics: {
        Row: {
          id: number;
          subject_id: number;
          name: string;
        };
        Insert: {
          id?: number;
          subject_id: number;
          name: string;
        };
        Update: {
          id?: number;
          subject_id?: number;
          name?: string;
        };
      };
      enem_skills: {
        Row: {
          id: number;
          code: string;
          description: string | null;
        };
        Insert: {
          id?: number;
          code: string;
          description?: string | null;
        };
        Update: {
          id?: number;
          code?: string;
          description?: string | null;
        };
      };
      questions: {
        Row: {
          id: string;
          topic_id: number | null;
          enem_skill_id: number | null;
          content: Json;
          correct_option: string;
          created_by_tutor_id: string | null;
          difficulty_level_by_tutor: number | null;
          ai_generated_explanation: string | null;
          tutor_validated_explanation: string | null;
          status: QuestionStatus;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          topic_id?: number | null;
          enem_skill_id?: number | null;
          content: Json;
          correct_option: string;
          created_by_tutor_id?: string | null;
          difficulty_level_by_tutor?: number | null;
          ai_generated_explanation?: string | null;
          tutor_validated_explanation?: string | null;
          status?: QuestionStatus;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          topic_id?: number | null;
          enem_skill_id?: number | null;
          content?: Json;
          correct_option?: string;
          created_by_tutor_id?: string | null;
          difficulty_level_by_tutor?: number | null;
          ai_generated_explanation?: string | null;
          tutor_validated_explanation?: string | null;
          status?: QuestionStatus;
          created_at?: string;
          updated_at?: string;
        };
      };
      student_answers: {
        Row: {
          id: number;
          student_id: string;
          question_id: string;
          selected_option: string | null;
          is_correct: boolean | null;
          response_time_ms: number | null;
          submitted_at: string;
        };
        Insert: {
          id?: number;
          student_id: string;
          question_id: string;
          selected_option?: string | null;
          is_correct?: boolean | null;
          response_time_ms?: number | null;
          submitted_at?: string;
        };
        Update: {
          id?: number;
          student_id?: string;
          question_id?: string;
          selected_option?: string | null;
          is_correct?: boolean | null;
          response_time_ms?: number | null;
          submitted_at?: string;
        };
      };
      student_interactions: {
        Row: {
          id: number;
          student_id: string;
          question_id: string | null;
          event_type: string;
          event_data: Json | null;
          occurred_at: string;
        };
        Insert: {
          id?: number;
          student_id: string;
          question_id?: string | null;
          event_type: string;
          event_data?: Json | null;
          occurred_at?: string;
        };
        Update: {
          id?: number;
          student_id?: string;
          question_id?: string | null;
          event_type?: string;
          event_data?: Json | null;
          occurred_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}
