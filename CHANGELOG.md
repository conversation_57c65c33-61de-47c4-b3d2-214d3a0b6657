# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added

- Modern Git workflow with simple-git-hooks replacing deprecated Husky
- Conventional commit message enforcement with commitlint
- Automated semantic versioning and releases
- Comprehensive GitHub Actions CI/CD workflows
- Automated dependency updates
- Enhanced code quality checks and formatting
- Git commit message template
- Comprehensive documentation for new Git workflow

### Changed

- Migrated from Husky to simple-git-hooks for better performance
- Enhanced package.json scripts for better development experience
- Updated .gitignore with modern development patterns

### Removed

- Deprecated <PERSON>sky configuration and dependencies

### Security

- Added automated security auditing in CI pipeline
- Implemented CodeQL analysis for security scanning

## Previous Changes

### Authentication System Refactor

- Added email, first_name, and last_name columns to profiles table
- Implemented automatic profile creation for existing users without profiles
- Improved error handling and retry logic in authentication processes
- Restructured routing to eliminate conflicts between student and tutor dashboards
- Enhanced middleware for better session management and role-based access control
- Updated documentation to reflect changes in authentication and profile management
- Created new SQL migration scripts for profile policies and structure
- Added PWA manifest for improved user experience on mobile devices
