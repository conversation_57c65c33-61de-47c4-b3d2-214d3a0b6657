name: Dependency Update

on:
  schedule:
    # Run every Monday at 9 AM UTC
    - cron: '0 9 * * 1'
  workflow_dispatch:

jobs:
  update-dependencies:
    name: Update Dependencies
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          
      - name: Update dependencies
        run: |
          npm update
          npm audit fix --force || true
          
      - name: Check for changes
        id: verify-changed-files
        run: |
          if [ -n "$(git status --porcelain)" ]; then
            echo "changed=true" >> $GITHUB_OUTPUT
          else
            echo "changed=false" >> $GITHUB_OUTPUT
          fi
          
      - name: Run tests
        if: steps.verify-changed-files.outputs.changed == 'true'
        run: |
          npm ci
          npm run lint
          npm run check-types
          npm run build
          npm test
          
      - name: Commit and create PR
        if: steps.verify-changed-files.outputs.changed == 'true'
        uses: peter-evans/create-pull-request@v6
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: 'chore(deps): update dependencies'
          title: 'chore(deps): automated dependency update'
          body: |
            This PR was automatically created to update dependencies.
            
            Please review the changes and ensure all tests pass before merging.
          branch: chore/dependency-update
          delete-branch: true
